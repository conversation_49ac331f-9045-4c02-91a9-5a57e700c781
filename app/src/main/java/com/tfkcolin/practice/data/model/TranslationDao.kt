package com.tfkcolin.practice.data.model

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow

@Dao
interface TranslationDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertTranslation(translation: Translation)

    @Update
    suspend fun updateTranslation(translation: Translation)

    @Query("SELECT * FROM translations ORDER BY timestamp DESC")
    fun getAllTranslations(): Flow<List<Translation>>

    @Query("SELECT * FROM translations WHERE id = :id")
    suspend fun getTranslationById(id: String): Translation?

    @Query("SELECT * FROM translations WHERE isStarred = 1 ORDER BY timestamp DESC")
    fun getStarredTranslations(): Flow<List<Translation>>

    @Query("DELETE FROM translations WHERE id = :id")
    suspend fun deleteTranslation(id: String)

    @Query("SELECT COUNT(*) FROM translations")
    suspend fun getTranslationCount(): Int

    @Query("SELECT * FROM translations WHERE sourceLanguage = :sourceLanguage ORDER BY timestamp DESC")
    suspend fun getTranslationsBySourceLanguage(sourceLanguage: String): List<Translation>
}