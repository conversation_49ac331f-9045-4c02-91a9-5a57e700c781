package com.tfkcolin.practice.features.practice.ui

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.tfkcolin.practice.data.model.PracticeItem
import com.tfkcolin.practice.ui.theme.*

@Composable
fun ListeningPracticeContent(
    item: PracticeItem,
    onPlayAudio: () -> Unit,
    onContinue: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        Spacer(modifier = Modifier.height(32.dp))
        
        // Main content display
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA)),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Text(
                    text = item.content,
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = PrimaryBlack,
                    textAlign = TextAlign.Center
                )

                if (item.pronunciation != null) {
                    Text(
                        text = "/${item.pronunciation}/",
                        style = MaterialTheme.typography.bodyLarge,
                        color = AccentBlue,
                        textAlign = TextAlign.Center
                    )
                }

                // Display translations
                if (item.translations.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = item.translations.joinToString(", "),
                        style = MaterialTheme.typography.bodyLarge,
                        color = PrimaryBlack.copy(alpha = 0.7f),
                        textAlign = TextAlign.Center,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        
        // Audio play button
        FloatingActionButton(
            onClick = onPlayAudio,
            modifier = Modifier.size(72.dp),
            containerColor = AccentBlue,
            contentColor = Color.White
        ) {
            Icon(
                imageVector = Icons.Default.VolumeUp,
                contentDescription = "Play pronunciation",
                modifier = Modifier.size(32.dp)
            )
        }
        
        Text(
            text = "Tap to hear the pronunciation",
            style = MaterialTheme.typography.bodyMedium,
            color = PrimaryBlack.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        // Continue button
        Button(
            onClick = onContinue,
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            colors = ButtonDefaults.buttonColors(containerColor = AccentBlue),
            shape = RoundedCornerShape(12.dp)
        ) {
            Text(
                text = "Continue to Speaking",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold
            )
        }
    }
}

@Composable
fun SpeakingPracticeContent(
    item: PracticeItem,
    userResponse: String,
    pronunciationScore: Float?,
    transcriptionResult: String?,
    showFeedback: Boolean,
    onStartRecording: () -> Unit,
    onStopRecording: () -> Unit,
    onRetry: () -> Unit,
    onContinue: () -> Unit
) {
    var isRecording by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Target text display
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA))
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = item.content,
                    style = MaterialTheme.typography.headlineSmall,
                    fontWeight = FontWeight.Bold,
                    color = PrimaryBlack,
                    textAlign = TextAlign.Center
                )

                if (item.pronunciation != null) {
                    Text(
                        text = "/${item.pronunciation}/",
                        style = MaterialTheme.typography.bodyMedium,
                        color = AccentBlue,
                        textAlign = TextAlign.Center
                    )
                }

                // Display translations
                if (item.translations.isNotEmpty()) {
                    Text(
                        text = item.translations.joinToString(", "),
                        style = MaterialTheme.typography.bodyLarge,
                        color = PrimaryBlack.copy(alpha = 0.7f),
                        textAlign = TextAlign.Center,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
        
        Text(
            text = "Now try to pronounce it yourself",
            style = MaterialTheme.typography.bodyLarge,
            color = PrimaryBlack.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
        
        // Recording button
        FloatingActionButton(
            onClick = {
                if (isRecording) {
                    onStopRecording()
                    isRecording = false
                } else {
                    onStartRecording()
                    isRecording = true
                }
            },
            modifier = Modifier.size(80.dp),
            containerColor = if (isRecording) Color(0xFFE53E3E) else Color(0xFF38A169),
            contentColor = Color.White
        ) {
            Icon(
                imageVector = if (isRecording) Icons.Default.Stop else Icons.Default.Mic,
                contentDescription = if (isRecording) "Stop recording" else "Start recording",
                modifier = Modifier.size(36.dp)
            )
        }
        
        Text(
            text = if (isRecording) "Recording... Tap to stop" else "Tap to start recording",
            style = MaterialTheme.typography.bodyMedium,
            color = PrimaryBlack.copy(alpha = 0.7f),
            textAlign = TextAlign.Center
        )
        
        // Feedback section
        AnimatedVisibility(
            visible = showFeedback,
            enter = slideInVertically() + fadeIn(),
            exit = slideOutVertically() + fadeOut()
        ) {
            FeedbackSection(
                targetText = item.content,
                userTranscription = transcriptionResult ?: "",
                pronunciationScore = pronunciationScore,
                onRetry = onRetry,
                onContinue = onContinue
            )
        }
        
        if (!showFeedback) {
            Spacer(modifier = Modifier.weight(1f))
        }
    }
}

@Composable
fun FeedbackSection(
    targetText: String,
    userTranscription: String,
    pronunciationScore: Float?,
    onRetry: () -> Unit,
    onContinue: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = when {
                pronunciationScore == null -> Color(0xFFFFF3CD)
                pronunciationScore >= 0.8f -> Color(0xFFD4EDDA)
                pronunciationScore >= 0.6f -> Color(0xFFFFF3CD)
                else -> Color(0xFFF8D7DA)
            }
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Score display
            pronunciationScore?.let { score ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = when {
                            score >= 0.8f -> Icons.Default.CheckCircle
                            score >= 0.6f -> Icons.Default.Warning
                            else -> Icons.Default.Error
                        },
                        contentDescription = null,
                        tint = when {
                            score >= 0.8f -> Color(0xFF155724)
                            score >= 0.6f -> Color(0xFF856404)
                            else -> Color(0xFF721C24)
                        }
                    )
                    Text(
                        text = "Pronunciation Score: ${(score * 100).toInt()}%",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.SemiBold,
                        color = when {
                            score >= 0.8f -> Color(0xFF155724)
                            score >= 0.6f -> Color(0xFF856404)
                            else -> Color(0xFF721C24)
                        }
                    )
                }
            }
            
            // Comparison
            if (userTranscription.isNotEmpty()) {
                Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                    Text(
                        text = "Target: $targetText",
                        style = MaterialTheme.typography.bodyMedium,
                        color = PrimaryBlack
                    )
                    Text(
                        text = "You said: $userTranscription",
                        style = MaterialTheme.typography.bodyMedium,
                        color = AccentBlue,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            
            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                OutlinedButton(
                    onClick = onRetry,
                    modifier = Modifier.weight(1f)
                ) {
                    Text("Try Again")
                }
                Button(
                    onClick = onContinue,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(containerColor = AccentBlue)
                ) {
                    Text("Continue")
                }
            }
        }
    }
}

@Composable
fun ComprehensionPracticeContent(
    item: PracticeItem,
    onAnswerSelected: (String) -> Unit,
    onContinue: () -> Unit
) {
    var selectedAnswer by remember { mutableStateOf<String?>(null) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Question
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(containerColor = Color(0xFFF8F9FA))
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Text(
                    text = "What does this mean?",
                    style = MaterialTheme.typography.titleLarge,
                    fontWeight = FontWeight.SemiBold,
                    color = PrimaryBlack
                )
                Text(
                    text = item.content,
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold,
                    color = AccentBlue,
                    textAlign = TextAlign.Center
                )
            }
        }
        
        // Answer options
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(8.dp),
            modifier = Modifier.weight(1f)
        ) {
            // Create some dummy options including the correct ones
            val allOptions = (item.translations + generateDummyOptions(item.translations)).shuffled()
            
            items(allOptions) { option ->
                AnswerOption(
                    text = option,
                    isSelected = selectedAnswer == option,
                    isCorrect = item.translations.contains(option),
                    showResult = selectedAnswer != null,
                    onClick = {
                        if (selectedAnswer == null) {
                            selectedAnswer = option
                            onAnswerSelected(option)
                        }
                    }
                )
            }
        }
        
        // Continue button
        AnimatedVisibility(
            visible = selectedAnswer != null,
            enter = slideInVertically() + fadeIn()
        ) {
            Button(
                onClick = onContinue,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                colors = ButtonDefaults.buttonColors(containerColor = AccentBlue),
                shape = RoundedCornerShape(12.dp)
            ) {
                Text(
                    text = "Continue",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }
    }
}

@Composable
fun AnswerOption(
    text: String,
    isSelected: Boolean,
    isCorrect: Boolean,
    showResult: Boolean,
    onClick: () -> Unit
) {
    val backgroundColor = when {
        showResult && isCorrect -> Color(0xFFD4EDDA)
        showResult && isSelected && !isCorrect -> Color(0xFFF8D7DA)
        isSelected -> AccentBlue.copy(alpha = 0.1f)
        else -> Color.White
    }
    
    val borderColor = when {
        showResult && isCorrect -> Color(0xFF28A745)
        showResult && isSelected && !isCorrect -> Color(0xFFDC3545)
        isSelected -> AccentBlue
        else -> Color(0xFFE0E0E0)
    }
    
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(enabled = !showResult) { onClick() }
            .border(
                width = 2.dp,
                color = borderColor,
                shape = RoundedCornerShape(12.dp)
            ),
        colors = CardDefaults.cardColors(containerColor = backgroundColor),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = text,
                style = MaterialTheme.typography.bodyLarge,
                color = PrimaryBlack,
                modifier = Modifier.weight(1f)
            )
            
            if (showResult) {
                Icon(
                    imageVector = if (isCorrect) Icons.Default.CheckCircle else if (isSelected) Icons.Default.Cancel else Icons.Default.RadioButtonUnchecked,
                    contentDescription = null,
                    tint = when {
                        isCorrect -> Color(0xFF28A745)
                        isSelected -> Color(0xFFDC3545)
                        else -> Color.Transparent
                    }
                )
            }
        }
    }
}

private fun generateDummyOptions(correctAnswers: List<String>): List<String> {
    // Generate some plausible wrong answers
    // In a real implementation, this would be more sophisticated
    val dummyOptions = listOf(
        "Example translation 1",
        "Example translation 2",
        "Example translation 3",
        "Another meaning",
        "Different translation"
    )
    
    return dummyOptions.filter { it !in correctAnswers }.take(3)
}
