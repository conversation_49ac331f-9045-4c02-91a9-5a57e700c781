package com.tfkcolin.practice.features.practice.domain

import com.tfkcolin.practice.data.model.*
import com.tfkcolin.practice.features.text.data.TranslationRepository
import kotlinx.coroutines.flow.first
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton
import kotlin.math.max
import kotlin.math.min

@Singleton
class PracticeContentSelector @Inject constructor(
    private val translationRepository: TranslationRepository,
    private val practiceItemDao: PracticeItemDao,
    private val itemMasteryDao: ItemMasteryDao,
    private val userPracticeProfileDao: UserPracticeProfileDao
) {

    suspend fun generatePracticeSession(
        userId: String,
        targetLanguage: String,
        criteria: ContentSelectionCriteria = ContentSelectionCriteria(targetLanguage = targetLanguage)
    ): PracticeSession {
        
        // 1. Get available translations in target language
        val availableTranslations = translationRepository
            .getTranslationsBySourceLanguage(targetLanguage)
            .filter { it.words.isNotEmpty() || it.expressions.isNotEmpty() }
        
        if (availableTranslations.isEmpty()) {
            throw IllegalStateException("No content available for language: $targetLanguage")
        }
        
        // 2. Select primary translation (prioritize recent, longer content)
        val primaryTranslation = selectPrimaryTranslation(availableTranslations)
        
        // 3. Extract and create practice items from primary translation
        val primaryItems = extractPracticeItemsFromTranslation(primaryTranslation, userId)
        
        // 4. Supplement with additional items if needed
        val allItems = if (primaryItems.size < criteria.sessionSize) {
            supplementWithAdditionalItems(
                primaryItems,
                availableTranslations.filter { it.id != primaryTranslation.id },
                userId,
                criteria.sessionSize
            )
        } else {
            selectBestItems(primaryItems, criteria.sessionSize, userId)
        }
        
        // 5. Sort items by learning priority
        val prioritizedItems = prioritizeItemsForLearning(allItems, userId, criteria)
        
        // 6. Determine session type
        val sessionType = determineSessionType(prioritizedItems, primaryTranslation)
        
        // 7. Get supplementary translations used
        val supplementaryTranslations = getSupplementaryTranslations(prioritizedItems, availableTranslations, primaryTranslation)
        
        return PracticeSession(
            sessionId = UUID.randomUUID().toString(),
            targetLanguage = targetLanguage,
            primaryTranslation = primaryTranslation,
            supplementaryTranslations = supplementaryTranslations,
            practiceItems = prioritizedItems.take(criteria.sessionSize),
            minimumItems = criteria.sessionSize,
            sessionType = sessionType
        )
    }
    
    private fun selectPrimaryTranslation(translations: List<Translation>): Translation {
        return translations.maxByOrNull { translation ->
            val contentLength = translation.originalText.length
            val wordCount = translation.words.size + translation.expressions.size
            val recency = translation.timestamp.toLongOrNull() ?: 0L
            
            // Scoring: prioritize content richness and recency
            (contentLength * 0.3) + (wordCount * 10.0) + (recency / 1000000.0) // Normalize timestamp
        } ?: translations.first()
    }
    
    private suspend fun extractPracticeItemsFromTranslation(
        translation: Translation,
        userId: String
    ): List<PracticeItem> {
        val items = mutableListOf<PracticeItem>()
        
        // Extract words
        translation.words.forEach { wordDetail ->
            val existingItem = practiceItemDao.getItemsByTranslation(translation.id)
                .find { it.content == wordDetail.word }
            
            val masteryInfo = itemMasteryDao.getMastery(userId, existingItem?.itemId ?: "")
            
            items.add(
                PracticeItem(
                    id = existingItem?.itemId ?: UUID.randomUUID().toString(),
                    content = wordDetail.word,
                    pronunciation = wordDetail.pronunciation,
                    translations = wordDetail.translations,
                    sourceTranslationId = translation.id,
                    itemType = PracticeItemType.WORD,
                    difficultyLevel = determineDifficultyLevel(wordDetail.word),
                    masteryScore = masteryInfo?.masteryLevel ?: 0f,
                    lastPracticed = masteryInfo?.lastPracticed,
                    practiceCount = masteryInfo?.practiceCount ?: 0,
                    isMarkedAsLearned = masteryInfo?.isMarkedAsLearned ?: false,
                    frequencyScore = calculateFrequencyScore(wordDetail.word, translation.originalText)
                )
            )
        }
        
        // Extract expressions
        translation.expressions.forEach { expressionExample ->
            val existingItem = practiceItemDao.getItemsByTranslation(translation.id)
                .find { it.content == expressionExample.sourceExpression }
            
            val masteryInfo = itemMasteryDao.getMastery(userId, existingItem?.itemId ?: "")
            
            items.add(
                PracticeItem(
                    id = existingItem?.itemId ?: UUID.randomUUID().toString(),
                    content = expressionExample.sourceExpression,
                    pronunciation = expressionExample.pronunciation,
                    translations = listOf(expressionExample.translatedExpression),
                    sourceTranslationId = translation.id,
                    itemType = PracticeItemType.EXPRESSION,
                    difficultyLevel = determineDifficultyLevel(expressionExample.sourceExpression),
                    masteryScore = masteryInfo?.masteryLevel ?: 0f,
                    lastPracticed = masteryInfo?.lastPracticed,
                    practiceCount = masteryInfo?.practiceCount ?: 0,
                    isMarkedAsLearned = masteryInfo?.isMarkedAsLearned ?: false,
                    frequencyScore = calculateFrequencyScore(expressionExample.sourceExpression, translation.originalText)
                )
            )
        }
        
        return items
    }
    
    private suspend fun supplementWithAdditionalItems(
        primaryItems: List<PracticeItem>,
        additionalTranslations: List<Translation>,
        userId: String,
        targetSize: Int
    ): List<PracticeItem> {
        val allItems = primaryItems.toMutableList()
        val needed = targetSize - primaryItems.size
        
        if (needed <= 0) return allItems
        
        // Get items from additional translations, prioritizing by various factors
        val supplementaryItems = mutableListOf<PracticeItem>()
        
        for (translation in additionalTranslations.sortedByDescending { it.words.size + it.expressions.size }) {
            if (supplementaryItems.size >= needed) break
            
            val translationItems = extractPracticeItemsFromTranslation(translation, userId)
            supplementaryItems.addAll(translationItems)
        }
        
        // Select best supplementary items
        val bestSupplementary = selectBestItems(supplementaryItems, needed, userId)
        allItems.addAll(bestSupplementary)
        
        return allItems
    }
    
    private suspend fun selectBestItems(
        items: List<PracticeItem>,
        count: Int,
        userId: String
    ): List<PracticeItem> {
        return items
            .filter { !it.isMarkedAsLearned } // Exclude learned items
            .sortedWith(compareBy<PracticeItem> { item ->
                // Priority score: lower mastery = higher priority
                val masteryWeight = (1.0f - item.masteryScore) * 0.4f
                val frequencyWeight = item.frequencyScore * 0.3f
                val timeWeight = getTimeSinceLastPracticeWeight(item) * 0.3f
                
                -(masteryWeight + frequencyWeight + timeWeight) // Negative for descending order
            })
            .take(count)
    }
    
    private suspend fun prioritizeItemsForLearning(
        items: List<PracticeItem>,
        userId: String,
        criteria: ContentSelectionCriteria
    ): List<PracticeItem> {
        val userProfile = userPracticeProfileDao.getProfile(userId, criteria.targetLanguage)
        
        return items.sortedWith(compareBy<PracticeItem> { item ->
            var priority = 0f
            
            // 1. Mastery-based priority (lower mastery = higher priority)
            priority += (1.0f - item.masteryScore) * 0.35f
            
            // 2. Frequency-based priority (more frequent = higher priority)
            priority += item.frequencyScore * 0.25f
            
            // 3. Time-based priority (longer since last practice = higher priority)
            priority += getTimeSinceLastPracticeWeight(item) * 0.25f
            
            // 4. Difficulty matching user preference
            if (userProfile != null) {
                val difficultyMatch = when {
                    item.difficultyLevel == userProfile.preferredDifficulty -> 0.1f
                    item.difficultyLevel.ordinal == userProfile.preferredDifficulty.ordinal - 1 -> 0.05f
                    item.difficultyLevel.ordinal == userProfile.preferredDifficulty.ordinal + 1 -> 0.05f
                    else -> 0f
                }
                priority += difficultyMatch * 0.15f
            }
            
            -priority // Negative for descending order
        })
    }
    
    private fun determineDifficultyLevel(content: String): DifficultyLevel {
        val length = content.length
        val wordCount = content.split("\\s+".toRegex()).size
        
        return when {
            length <= 5 && wordCount == 1 -> DifficultyLevel.BEGINNER
            length <= 15 && wordCount <= 3 -> DifficultyLevel.INTERMEDIATE
            else -> DifficultyLevel.ADVANCED
        }
    }
    
    private fun calculateFrequencyScore(content: String, originalText: String): Float {
        val occurrences = originalText.lowercase().split("\\W+".toRegex())
            .count { it.contains(content.lowercase()) }
        
        // Normalize frequency score (0.0 to 1.0)
        return min(occurrences / 10f, 1.0f)
    }
    
    private fun getTimeSinceLastPracticeWeight(item: PracticeItem): Float {
        val lastPracticed = item.lastPracticed ?: return 1.0f // Never practiced = highest weight
        val daysSince = (System.currentTimeMillis() - lastPracticed) / (24 * 60 * 60 * 1000)
        
        return when {
            daysSince >= 7 -> 1.0f      // Week or more = highest priority
            daysSince >= 3 -> 0.7f      // 3-7 days = high priority
            daysSince >= 1 -> 0.4f      // 1-3 days = medium priority
            else -> 0.1f                // Less than a day = low priority
        }
    }
    
    private fun determineSessionType(items: List<PracticeItem>, primaryTranslation: Translation): SessionType {
        val primaryItems = items.count { it.sourceTranslationId == primaryTranslation.id }
        val totalItems = items.size
        
        return when {
            primaryItems == totalItems -> SessionType.SINGLE_TEXT
            items.any { it.practiceCount > 0 } -> SessionType.REVIEW
            else -> SessionType.MIXED
        }
    }
    
    private fun getSupplementaryTranslations(
        items: List<PracticeItem>,
        allTranslations: List<Translation>,
        primaryTranslation: Translation
    ): List<Translation> {
        val supplementaryIds = items
            .map { it.sourceTranslationId }
            .distinct()
            .filter { it != primaryTranslation.id }
        
        return allTranslations.filter { it.id in supplementaryIds }
    }
}
