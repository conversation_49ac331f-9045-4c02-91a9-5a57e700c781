package com.tfkcolin.practice.features.text.data

import android.app.Application
import androidx.room.Room
import com.tfkcolin.practice.data.model.AppDatabase
import com.tfkcolin.practice.data.model.ExpressionStats
import com.tfkcolin.practice.data.model.Translation
import com.tfkcolin.practice.data.model.WordStats
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class TranslationRepository @Inject constructor(
    private val application: Application
) {

    private val database: AppDatabase by lazy {
        Room.databaseBuilder(
            application,
            AppDatabase::class.java,
            AppDatabase.DATABASE_NAME
        ).build()
    }

    private val translationDao by lazy { database.translationDao() }
    private val frequencyStatsDao by lazy { database.frequencyStatsDao() }

    // Translation operations
    fun getAllTranslations(): Flow<List<Translation>> = translationDao.getAllTranslations()

    fun getStarredTranslations(): Flow<List<Translation>> = translationDao.getStarredTranslations()

    suspend fun getTranslationById(id: String): Translation? = translationDao.getTranslationById(id)

    suspend fun saveTranslation(translation: Translation) {
        translationDao.insertTranslation(translation)
        updateFrequencyStats(translation)
    }

    suspend fun updateTranslation(translation: Translation) {
        translationDao.updateTranslation(translation)
    }

    suspend fun deleteTranslation(id: String) {
        translationDao.deleteTranslation(id)
    }

    suspend fun getTranslationCount(): Int = translationDao.getTranslationCount()

    suspend fun getTranslationsBySourceLanguage(sourceLanguage: String): List<Translation> =
        translationDao.getTranslationsBySourceLanguage(sourceLanguage)

    // Frequency stats operations
    fun getAllWordStats(): Flow<List<WordStats>> = frequencyStatsDao.getAllWordStats()

    fun getAllExpressionStats(): Flow<List<ExpressionStats>> = frequencyStatsDao.getAllExpressionStats()

    suspend fun getTopWordStats(limit: Int): List<WordStats> = frequencyStatsDao.getTopWordStats(limit)

    suspend fun getTopExpressionStats(limit: Int): List<ExpressionStats> = frequencyStatsDao.getTopExpressionStats(limit)

    private suspend fun updateFrequencyStats(translation: Translation) {
        // Update word frequencies
        for (word in translation.words) {
            val existing = frequencyStatsDao.getWordStat(word.normalizedWord)
            if (existing != null) {
                val updated = existing.copy(
                    encounterCount = existing.encounterCount + 1,
                    lastEncountered = System.currentTimeMillis()
                )
                frequencyStatsDao.updateWordStat(updated)
            } else {
                val newStat = WordStats(
                    normalizedWord = word.normalizedWord,
                    encounterCount = 1,
                    lastEncountered = System.currentTimeMillis()
                )
                frequencyStatsDao.insertWordStat(newStat)
            }
        }

        // Update expression frequencies
        for (expression in translation.expressions) {
            val existing = frequencyStatsDao.getExpressionStat(expression.normalizedExpression)
            if (existing != null) {
                val updated = existing.copy(
                    encounterCount = existing.encounterCount + 1,
                    lastEncountered = System.currentTimeMillis()
                )
                frequencyStatsDao.updateExpressionStat(updated)
            } else {
                val newStat = ExpressionStats(
                    normalizedExpression = expression.normalizedExpression,
                    encounterCount = 1,
                    lastEncountered = System.currentTimeMillis()
                )
                frequencyStatsDao.insertExpressionStat(newStat)
            }
        }
    }
}